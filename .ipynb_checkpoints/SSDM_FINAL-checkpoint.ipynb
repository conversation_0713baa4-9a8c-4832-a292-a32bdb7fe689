{"cells": [{"cell_type": "code", "execution_count": null, "id": "bx1JlLE2j_mz", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bx1JlLE2j_mz", "outputId": "75c27294-2c0d-4b8a-88d1-5ef7c75dfc34"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting liac-arff\n", "  Using cached liac-arff-2.5.0.tar.gz (13 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25lerror\n", "  \u001b[1;31merror\u001b[0m: \u001b[1msubprocess-exited-with-error\u001b[0m\n", "  \n", "  \u001b[31m×\u001b[0m \u001b[32mpython setup.py egg_info\u001b[0m did not run successfully.\n", "  \u001b[31m│\u001b[0m exit code: \u001b[1;36m1\u001b[0m\n", "  \u001b[31m╰─>\u001b[0m \u001b[31m[1 lines of output]\u001b[0m\n", "  \u001b[31m   \u001b[0m ERROR: Can not execute `setup.py` since setuptools is not available in the build environment.\n", "  \u001b[31m   \u001b[0m \u001b[31m[end of output]\u001b[0m\n", "  \n", "  \u001b[1;35mnote\u001b[0m: This error originates from a subprocess, and is likely not a problem with pip.\n", "\u001b[?25h\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "\u001b[1;31merror\u001b[0m: \u001b[1mmetadata-generation-failed\u001b[0m\n", "\n", "\u001b[31m×\u001b[0m Encountered error while generating package metadata.\n", "\u001b[31m╰─>\u001b[0m See above for output.\n", "\n", "\u001b[1;35mnote\u001b[0m: This is an issue with the package mentioned above, not pip.\n", "\u001b[1;36mhint\u001b[0m: See above for details.\n"]}], "source": ["!pip install liac-arff pymining"]}, {"cell_type": "code", "execution_count": null, "id": "266c56d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (25.1.1)\n", "Collecting pip\n", "  Downloading pip-25.2-py3-none-any.whl.metadata (4.7 kB)\n", "Requirement already satisfied: setuptools in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (65.5.0)\n", "Collecting setuptools\n", "  Downloading setuptools-80.9.0-py3-none-any.whl.metadata (6.6 kB)\n", "Downloading pip-25.2-py3-none-any.whl (1.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m1.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading setuptools-80.9.0-py3-none-any.whl (1.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m644.3 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: setuptools, pip\n", "\u001b[2K  Attempting uninstall: setuptools\n", "\u001b[2K    Found existing installation: setuptools 65.5.0\n", "\u001b[2K    Uninstalling setuptools-65.5.0:\n", "\u001b[2K      Successfully uninstalled setuptools-65.5.0\u001b[0m \u001b[32m0/2\u001b[0m [setuptools]\n", "\u001b[2K  Attempting uninstall: pip━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0/2\u001b[0m [setuptools]\n", "\u001b[2K    Found existing installation: pip 25.1.1━\u001b[0m \u001b[32m0/2\u001b[0m [setuptools]\n", "\u001b[2K    Uninstalling pip-25.1.1:━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0/2\u001b[0m [setuptools]\n", "\u001b[2K      Successfully uninstalled pip-25.1.1m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/2\u001b[0m [pip]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/2\u001b[0m [pip]\u001b[32m1/2\u001b[0m [pip]\n", "\u001b[1A\u001b[2KSuccessfully installed pip-25.2 setuptools-80.9.0\n", "Note: you may need to restart the kernel to use updated packages.\n", "Collecting liac-arff\n", "  Using cached liac-arff-2.5.0.tar.gz (13 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting pymining\n", "  Downloading pymining-0.2-py2.py3-none-any.whl.metadata (910 bytes)\n", "Downloading pymining-0.2-py2.py3-none-any.whl (9.4 kB)\n", "Building wheels for collected packages: liac-arff\n", "  Building wheel for liac-arff (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for liac-arff: filename=liac_arff-2.5.0-py3-none-any.whl size=11768 sha256=c94611a4ff5eeaf5adc6f335da12cd0622fffe3abd28c9dffd35f37007638129\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/00/23/31/5e562fce1f95aabe57f2a7320d07433ba1cd152bcde2f6a002\n", "Successfully built liac-arff\n", "Installing collected packages: pymining, liac-arff\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/2\u001b[0m [liac-arff]\n", "\u001b[1A\u001b[2KSuccessfully installed liac-arff-2.5.0 pymining-0.2\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade pip setuptools\n", "%pip install liac-arff pymining\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8cf7f192", "metadata": {"id": "8cf7f192"}, "outputs": [], "source": ["import arff\n", "import pandas as pd\n", "import time\n", "from collections import defaultdict\n", "from itertools import combinations\n", "from pymining import seqmining"]}, {"cell_type": "code", "execution_count": 9, "id": "DrsaMfv5EDai", "metadata": {"id": "DrsaMfv5EDai"}, "outputs": [], "source": ["with open('/Users/<USER>/Desktop/untitled folder/PhishingData.arff ') as f:\n", "    data = arff.load(f)\n", "df = pd.DataFrame(data['data'], columns=[attr[0] for attr in data['attributes']])"]}, {"cell_type": "code", "execution_count": null, "id": "d1ed9f71", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d1ed9f71", "outputId": "14885f94-c600-4d6a-b1cf-e9665cf0cd50"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Sample Sequence:\n", "  1. SFH_pos\n", "  2. popUpWidnow_pos\n", "  3. SSLfinal_State_pos\n", "  4. Request_URL_pos\n", "  5. U<PERSON>_<PERSON>_<PERSON><PERSON>_pos\n", "  6. web_traffic_pos\n", "  7. URL_Length_pos\n", "  8. age_of_domain_pos\n", "  9. having_IP_Address_pos\n"]}], "source": ["def discretize_row(row):\n", "    return [f\"{col}_{'neg' if v == -1 else 'zero' if v == 0 else 'pos'}\"\n", "            for col, v in zip(df.columns[:-1], row[:-1])]\n", "\n", "sequences = df.apply(discretize_row, axis=1).tolist()\n", "\n", "print(\" Sample Sequence:\")\n", "for i, item in enumerate(sequences[0][:10]):\n", "    print(f\"  {i+1}. {item}\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "F4GzETeXE04F", "metadata": {"id": "F4GzETeXE04F"}, "outputs": [], "source": ["results = []"]}, {"cell_type": "code", "execution_count": 12, "id": "3AGJXcmelgRx", "metadata": {"id": "3AGJXcmelgRx"}, "outputs": [], "source": ["def run_gsp(sequences, min_support=3):\n", "    start = time.time()\n", "    patterns = list(seqmining.freq_seq_enum(sequences, min_support))\n", "    return patterns, time.time() - start"]}, {"cell_type": "code", "execution_count": null, "id": "ufdWtymAlgOM", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ufdWtymAlgOM", "outputId": "53ae5c44-39e8-42f8-f0fa-d7f7d1024627"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Simplified GSP for different min_support values ---\n", "\n", "Sample sequence (Simplified GSP - first 5 itemsets):\n", "  1. ['S', 'F', 'H', '_', 'p', 'o', 's']\n", "  2. ['p', 'o', 'p', 'U', 'p', 'W', 'i', 'd', 'n', 'o', 'w', '_', 'p', 'o', 's']\n", "  3. ['S', 'S', 'L', 'f', 'i', 'n', 'a', 'l', '_', 'S', 't', 'a', 't', 'e', '_', 'p', 'o', 's']\n", "  4. ['R', 'e', 'q', 'u', 'e', 's', 't', '_', 'U', 'R', 'L', '_', 'p', 'o', 's']\n", "  5. ['U', 'R', 'L', '_', 'o', 'f', '_', 'A', 'n', 'c', 'h', 'o', 'r', '_', 'p', 'o', 's']\n", "  6. ['w', 'e', 'b', '_', 't', 'r', 'a', 'f', 'f', 'i', 'c', '_', 'p', 'o', 's']\n", "  7. ['U', 'R', 'L', '_', 'L', 'e', 'n', 'g', 't', 'h', '_', 'p', 'o', 's']\n", "  8. ['a', 'g', 'e', '_', 'o', 'f', '_', 'd', 'o', 'm', 'a', 'i', 'n', '_', 'p', 'o', 's']\n", "  9. ['h', 'a', 'v', 'i', 'n', 'g', '_', 'I', 'P', '_', 'A', 'd', 'd', 'r', 'e', 's', 's', '_', 'p', 'o', 's']\n", "\n", "--- Running Simplified GSP with min_support=3 ---\n", "Simplified G<PERSON> completed in 0.1936 seconds.\n", "Simplified GSP with min_support=3 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.1936 seconds.\n", "\n", "--- Running Simplified GSP with min_support=4 ---\n", "Simplified GSP completed in 0.1493 seconds.\n", "Simplified GSP with min_support=4 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.1493 seconds.\n", "\n", "--- Running Simplified GSP with min_support=5 ---\n", "Simplified GSP completed in 0.1749 seconds.\n", "Simplified GSP with min_support=5 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.1749 seconds.\n"]}], "source": ["print(\"\\n--- Running Simplified GSP for different min_support values ---\")\n", "print(\"\\nSample sequence (Simplified GSP - first 5 itemsets):\")\n", "for i, item in enumerate(sequences[0][:10]):\n", "    print(f\"  {i+1}. {list(item)}\")\n", "\n", "for min_sup in [3, 4, 5]:\n", "    print(f\"\\n--- Running Simplified GSP with min_support={min_sup} ---\")\n", "    gsp_patterns, gsp_time = run_gsp(sequences, min_support=min_sup)\n", "    print(f\"Simplified GSP completed in {gsp_time:.4f} seconds.\")\n", "    print(f\"Simplified GSP with min_support={min_sup} completed. <PERSON><PERSON><PERSON> found: {len(gsp_patterns)}, Runtime: {gsp_time:.4f} seconds.\")\n", "\n", "    results.append({\n", "        \"Algorithm\": \"GSP (Simplified)\",\n", "        \"Min Support\": min_sup,\n", "        \"Patterns Found\": len(gsp_patterns),\n", "        \"Runtime (s)\": round(gsp_time, 6)\n", "    })\n"]}, {"cell_type": "code", "execution_count": 14, "id": "unN16F1ZlgL8", "metadata": {"id": "unN16F1ZlgL8"}, "outputs": [], "source": ["def run_prefixspan(sequences, min_support=3):\n", "    def mine(seqs, prefix=[]):\n", "        results = []\n", "        items = defaultdict(int)\n", "        for seq in seqs:\n", "            counted = set()\n", "            for item in seq:\n", "                if item not in counted:\n", "                    items[item] += 1\n", "                    counted.add(item)\n", "        for item, count in items.items():\n", "            if count >= min_support:\n", "                new_prefix = prefix + [item]\n", "                results.append((count, new_prefix))\n", "                projected = [s[s.index(item)+1:] for s in seqs if item in s]\n", "                results.extend(mine(projected, new_prefix))\n", "        return results\n", "    start = time.time()\n", "    patterns = mine(sequences)\n", "    return patterns, time.time() - start"]}, {"cell_type": "code", "execution_count": null, "id": "j4IPoSCemOgU", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "j4IPoSCemOgU", "outputId": "c2e77672-1d0b-4157-ef15-b2b7560124d3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running PrefixSpan for different min_support values ---\n", "\n", "Sample sequence (PrefixSpan - first 5 itemsets):\n", "  1. ['S', 'F', 'H', '_', 'p', 'o', 's']\n", "  2. ['p', 'o', 'p', 'U', 'p', 'W', 'i', 'd', 'n', 'o', 'w', '_', 'p', 'o', 's']\n", "  3. ['S', 'S', 'L', 'f', 'i', 'n', 'a', 'l', '_', 'S', 't', 'a', 't', 'e', '_', 'p', 'o', 's']\n", "  4. ['R', 'e', 'q', 'u', 'e', 's', 't', '_', 'U', 'R', 'L', '_', 'p', 'o', 's']\n", "  5. ['U', 'R', 'L', '_', 'o', 'f', '_', 'A', 'n', 'c', 'h', 'o', 'r', '_', 'p', 'o', 's']\n", "  6. ['w', 'e', 'b', '_', 't', 'r', 'a', 'f', 'f', 'i', 'c', '_', 'p', 'o', 's']\n", "  7. ['U', 'R', 'L', '_', 'L', 'e', 'n', 'g', 't', 'h', '_', 'p', 'o', 's']\n", "  8. ['a', 'g', 'e', '_', 'o', 'f', '_', 'd', 'o', 'm', 'a', 'i', 'n', '_', 'p', 'o', 's']\n", "  9. ['h', 'a', 'v', 'i', 'n', 'g', '_', 'I', 'P', '_', 'A', 'd', 'd', 'r', 'e', 's', 's', '_', 'p', 'o', 's']\n", "\n", "--- Running PrefixSpan with min_support=3 ---\n", "PrefixSpan completed in 0.2070 seconds.\n", "PrefixSpan with min_support=3 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.2070 seconds.\n", "\n", "--- Running PrefixSpan with min_support=4 ---\n", "PrefixSpan completed in 0.2031 seconds.\n", "PrefixSpan with min_support=4 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.2031 seconds.\n", "\n", "--- Running PrefixSpan with min_support=5 ---\n", "PrefixSpan completed in 0.2230 seconds.\n", "PrefixSpan with min_support=5 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 0.2230 seconds.\n"]}], "source": ["\n", "print(\"\\n--- Running PrefixSpan for different min_support values ---\")\n", "print(\"\\nSample sequence (PrefixSpan - first 5 itemsets):\")\n", "for i, item in enumerate(sequences[0][:10]):\n", "    print(f\"  {i+1}. {list(item)}\")\n", "\n", "for min_sup in [3, 4, 5]:\n", "    print(f\"\\n--- Running PrefixSpan with min_support={min_sup} ---\")\n", "    ps_patterns, ps_time = run_prefixspan(sequences, min_support=min_sup)\n", "    print(f\"PrefixSpan completed in {ps_time:.4f} seconds.\")\n", "    print(f\"PrefixSpan with min_support={min_sup} completed. <PERSON><PERSON><PERSON> found: {len(ps_patterns)}, Runtime: {ps_time:.4f} seconds.\")\n", "\n", "    results.append({\n", "        \"Algorithm\": \"PrefixSpan\",\n", "        \"Min Support\": min_sup,\n", "        \"Patterns Found\": len(ps_patterns),\n", "        \"Runtime (s)\": round(ps_time, 6)\n", "    })\n"]}, {"cell_type": "code", "execution_count": 17, "id": "7CZJRM6jlgGc", "metadata": {"id": "7CZJRM6jlgGc"}, "outputs": [], "source": ["def run_spade_simplified(sequences, min_support=3):\n", "    start = time.time()\n", "    vertical = defaultdict(list)\n", "    for sid, seq in enumerate(sequences):\n", "        for t, item in enumerate(seq):\n", "            vertical[item].append((sid, t))\n", "    freq1 = { (item,): occs for item, occs in vertical.items()\n", "              if len(set(sid for sid, _ in occs)) >= min_support }\n", "    all_patterns = {k: len(set(sid for sid, _ in v)) for k, v in freq1.items()}\n", "    current = freq1\n", "    while current:\n", "        next_level = defaultdict(list)\n", "        for s1 in current:\n", "            for s2 in freq1:\n", "                candidate = s1 + s2\n", "                occ1 = current[s1]\n", "                occ2 = freq1[s2]\n", "                joined = [(sid1, t2) for sid1, t1 in occ1\n", "                          for sid2, t2 in occ2 if sid1 == sid2 and t2 > t1]\n", "                if len(set(sid for sid, _ in joined)) >= min_support:\n", "                    next_level[candidate] = joined\n", "                    all_patterns[candidate] = len(set(sid for sid, _ in joined))\n", "        current = next_level\n", "    return [(v, [[i] for i in p]) for p, v in all_patterns.items()], time.time() - start\n", "\n", "def is_subsequence(subseq, seq):\n", "    it = iter(seq)\n", "    return all(item in it for item in subseq)"]}, {"cell_type": "code", "execution_count": null, "id": "B3G_G1Arlyyr", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 634}, "id": "B3G_G1Arlyyr", "outputId": "c7e4fbff-1ba6-47f0-a808-f0e679fac865"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Simplified SPADE for different min_support values ---\n", "\n", "Sample sequence (Simplified SPADE - first 5 itemsets):\n", "  1. ['S', 'F', 'H', '_', 'p', 'o', 's']\n", "  2. ['p', 'o', 'p', 'U', 'p', 'W', 'i', 'd', 'n', 'o', 'w', '_', 'p', 'o', 's']\n", "  3. ['S', 'S', 'L', 'f', 'i', 'n', 'a', 'l', '_', 'S', 't', 'a', 't', 'e', '_', 'p', 'o', 's']\n", "  4. ['R', 'e', 'q', 'u', 'e', 's', 't', '_', 'U', 'R', 'L', '_', 'p', 'o', 's']\n", "  5. ['U', 'R', 'L', '_', 'o', 'f', '_', 'A', 'n', 'c', 'h', 'o', 'r', '_', 'p', 'o', 's']\n", "  6. ['w', 'e', 'b', '_', 't', 'r', 'a', 'f', 'f', 'i', 'c', '_', 'p', 'o', 's']\n", "  7. ['U', 'R', 'L', '_', 'L', 'e', 'n', 'g', 't', 'h', '_', 'p', 'o', 's']\n", "  8. ['a', 'g', 'e', '_', 'o', 'f', '_', 'd', 'o', 'm', 'a', 'i', 'n', '_', 'p', 'o', 's']\n", "  9. ['h', 'a', 'v', 'i', 'n', 'g', '_', 'I', 'P', '_', 'A', 'd', 'd', 'r', 'e', 's', 's', '_', 'p', 'o', 's']\n", "\n", "--- Running Simplified SPADE with min_support=3 ---\n", "Simplified SPADE completed in 68.3367 seconds.\n", "Simplified SPADE with min_support=3 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 68.3367 seconds.\n", "\n", "--- Running Simplified SPADE with min_support=4 ---\n", "Simplified SPADE completed in 68.0748 seconds.\n", "Simplified SPADE with min_support=4 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 68.0748 seconds.\n", "\n", "--- Running Simplified SPADE with min_support=5 ---\n", "Simplified SPADE completed in 70.9546 seconds.\n", "Simplified SPADE with min_support=5 completed. <PERSON><PERSON><PERSON> found: 511, Runtime: 70.9546 seconds.\n"]}], "source": ["print(\"\\n--- Running Simplified SPADE for different min_support values ---\")\n", "print(\"\\nSample sequence (Simplified SPADE - first 5 itemsets):\")\n", "for i, item in enumerate(sequences[0][:10]):\n", "    print(f\"  {i+1}. {list(item)}\")\n", "\n", "for min_sup in [3, 4, 5]:\n", "    print(f\"\\n--- Running Simplified SPADE with min_support={min_sup} ---\")\n", "    spade_patterns, spade_time = run_spade_simplified(sequences, min_support=min_sup)\n", "    print(f\"Simplified SPADE completed in {spade_time:.4f} seconds.\")\n", "    print(f\"Simplified SPADE with min_support={min_sup} completed. <PERSON><PERSON><PERSON> found: {len(spade_patterns)}, Runtime: {spade_time:.4f} seconds.\")\n", "\n", "    results.append({\n", "        \"Algorithm\": \"SPADE (Simplified)\",\n", "        \"Min Support\": min_sup,\n", "        \"Patterns Found\": len(spade_patterns),\n", "        \"Runtime (s)\": round(spade_time, 6)\n", "    })\n"]}, {"cell_type": "code", "execution_count": 19, "id": "BypoKaa5lypW", "metadata": {"id": "BypoKaa5lypW"}, "outputs": [], "source": ["def run_apriori_all(sequences, min_support=3, max_len=3):\n", "    start = time.time()\n", "    item_counts = defaultdict(int)\n", "    for seq in sequences:\n", "        for item in seq:\n", "            item_counts[(item,)] += 1\n", "    freq_patterns = {k: v for k, v in item_counts.items() if v >= min_support}\n", "    all_patterns = dict(freq_patterns)\n", "    current_Lk = list(freq_patterns.keys())\n", "    k = 2\n", "    while current_Lk and k <= max_len:\n", "        candidate_counts = defaultdict(int)\n", "        for i in range(len(current_Lk)):\n", "            for j in range(len(current_Lk)):\n", "                a, b = current_Lk[i], current_Lk[j]\n", "                if a[1:] == b[:-1]:\n", "                    candidate = a + (b[-1],)\n", "                    for seq in sequences:\n", "                        if is_subsequence(candidate, seq):\n", "                            candidate_counts[candidate] += 1\n", "        current_Lk = [k for k, v in candidate_counts.items() if v >= min_support]\n", "        all_patterns.update({k: v for k, v in candidate_counts.items() if v >= min_support})\n", "        k += 1\n", "    return [(v, [[item for item in k]]) for k, v in all_patterns.items()], time.time() - start"]}, {"cell_type": "code", "execution_count": null, "id": "SVnNl4DWlyhf", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SVnNl4DWlyhf", "outputId": "a7a30ca2-9732-47fb-8ed1-f883bcf7c5f6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Simplified AprioriAll for different min_support values ---\n", "\n", "Sample sequence (Simplified AprioriAll - first 5 itemsets):\n", "  1. ['S', 'F', 'H', '_', 'p', 'o', 's']\n", "  2. ['p', 'o', 'p', 'U', 'p', 'W', 'i', 'd', 'n', 'o', 'w', '_', 'p', 'o', 's']\n", "  3. ['S', 'S', 'L', 'f', 'i', 'n', 'a', 'l', '_', 'S', 't', 'a', 't', 'e', '_', 'p', 'o', 's']\n", "  4. ['R', 'e', 'q', 'u', 'e', 's', 't', '_', 'U', 'R', 'L', '_', 'p', 'o', 's']\n", "  5. ['U', 'R', 'L', '_', 'o', 'f', '_', 'A', 'n', 'c', 'h', 'o', 'r', '_', 'p', 'o', 's']\n", "  6. ['w', 'e', 'b', '_', 't', 'r', 'a', 'f', 'f', 'i', 'c', '_', 'p', 'o', 's']\n", "  7. ['U', 'R', 'L', '_', 'L', 'e', 'n', 'g', 't', 'h', '_', 'p', 'o', 's']\n", "  8. ['a', 'g', 'e', '_', 'o', 'f', '_', 'd', 'o', 'm', 'a', 'i', 'n', '_', 'p', 'o', 's']\n", "  9. ['h', 'a', 'v', 'i', 'n', 'g', '_', 'I', 'P', '_', 'A', 'd', 'd', 'r', 'e', 's', 's', '_', 'p', 'o', 's']\n", "\n", "--- Running Simplified AprioriAll with min_support=3 ---\n", "Simplified <PERSON><PERSON><PERSON><PERSON><PERSON> completed in 0.0841 seconds.\n", "Simplified AprioriAll with min_support=3 completed. <PERSON><PERSON><PERSON> found: 129, Runtime: 0.0841 seconds.\n", "\n", "--- Running Simplified AprioriAll with min_support=4 ---\n", "Simplified <PERSON><PERSON><PERSON><PERSON><PERSON> completed in 0.0648 seconds.\n", "Simplified AprioriAll with min_support=4 completed. <PERSON><PERSON><PERSON> found: 129, Runtime: 0.0648 seconds.\n", "\n", "--- Running Simplified AprioriAll with min_support=5 ---\n", "Simplified <PERSON><PERSON><PERSON><PERSON><PERSON> completed in 0.0636 seconds.\n", "Simplified AprioriAll with min_support=5 completed. <PERSON><PERSON><PERSON> found: 129, Runtime: 0.0636 seconds.\n"]}], "source": ["print(\"\\n--- Running Simplified AprioriAll for different min_support values ---\")\n", "print(\"\\nSample sequence (Simplified AprioriAll - first 5 itemsets):\")\n", "for i, item in enumerate(sequences[0][:10]):\n", "    print(f\"  {i+1}. {list(item)}\")\n", "\n", "for min_sup in [3, 4, 5]:\n", "    print(f\"\\n--- Running Simplified AprioriAll with min_support={min_sup} ---\")\n", "    apriori_patterns, apriori_time = run_apriori_all(sequences, min_support=min_sup)\n", "    print(f\"Simplified AprioriAll completed in {apriori_time:.4f} seconds.\")\n", "    print(f\"Simplified AprioriAll with min_support={min_sup} completed. Pattern<PERSON> found: {len(apriori_patterns)}, Runtime: {apriori_time:.4f} seconds.\")\n", "\n", "    results.append({\n", "        \"Algorithm\": \"<PERSON><PERSON><PERSON><PERSON>ll (Simplified)\",\n", "        \"Min Support\": min_sup,\n", "        \"Patterns Found\": len(apriori_patterns),\n", "        \"Runtime (s)\": round(apriori_time, 6)\n", "    })\n"]}, {"cell_type": "code", "execution_count": null, "id": "23b29b59", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 703}, "id": "23b29b59", "outputId": "e8508a5c-7e34-43e4-c721-522827c30f8d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Algorithm Performance Metrics ---\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Algorithm</th>\n", "      <th>Min Support</th>\n", "      <th>Patterns Found</th>\n", "      <th>Runtime (s)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GSP (Simplified)</td>\n", "      <td>3</td>\n", "      <td>511</td>\n", "      <td>0.193617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GSP (Simplified)</td>\n", "      <td>4</td>\n", "      <td>511</td>\n", "      <td>0.149299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GSP (Simplified)</td>\n", "      <td>5</td>\n", "      <td>511</td>\n", "      <td>0.174903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PrefixSpan</td>\n", "      <td>3</td>\n", "      <td>511</td>\n", "      <td>0.207044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PrefixSpan</td>\n", "      <td>4</td>\n", "      <td>511</td>\n", "      <td>0.203112</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PrefixSpan</td>\n", "      <td>5</td>\n", "      <td>511</td>\n", "      <td>0.222952</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SPADE (Simplified)</td>\n", "      <td>3</td>\n", "      <td>511</td>\n", "      <td>68.336685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SPADE (Simplified)</td>\n", "      <td>4</td>\n", "      <td>511</td>\n", "      <td>68.074844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SPADE (Simplified)</td>\n", "      <td>5</td>\n", "      <td>511</td>\n", "      <td>70.954607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>AprioriAll (Simplified)</td>\n", "      <td>3</td>\n", "      <td>129</td>\n", "      <td>0.084140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>AprioriAll (Simplified)</td>\n", "      <td>4</td>\n", "      <td>129</td>\n", "      <td>0.064809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>AprioriAll (Simplified)</td>\n", "      <td>5</td>\n", "      <td>129</td>\n", "      <td>0.063629</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Algorithm  Min Support  Patterns Found  Runtime (s)\n", "0          GSP (Simplified)            3             511     0.193617\n", "1          GSP (Simplified)            4             511     0.149299\n", "2          GSP (Simplified)            5             511     0.174903\n", "3                PrefixSpan            3             511     0.207044\n", "4                PrefixSpan            4             511     0.203112\n", "5                PrefixSpan            5             511     0.222952\n", "6        SPADE (Simplified)            3             511    68.336685\n", "7        SPADE (Simplified)            4             511    68.074844\n", "8        SPADE (Simplified)            5             511    70.954607\n", "9   AprioriAll (Simplified)            3             129     0.084140\n", "10  AprioriAll (Simplified)            4             129     0.064809\n", "11  Apr<PERSON>i<PERSON>ll (Simplified)            5             129     0.063629"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Best and Worst Performers (Based on Runtime) ---\n", "\n", "Min Support = 3:\n", "  Best Performer: <PERSON><PERSON><PERSON><PERSON><PERSON> (Simplified) (Runtime: 0.0841 s)\n", "  Worst Performer: SPADE (Simplified) (Runtime: 68.3367 s)\n", "\n", "Min Support = 4:\n", "  Best Performer: <PERSON><PERSON><PERSON><PERSON><PERSON> (Simplified) (Runtime: 0.0648 s)\n", "  Worst Performer: SPADE (Simplified) (Runtime: 68.0748 s)\n", "\n", "Min Support = 5:\n", "  Best Performer: <PERSON><PERSON><PERSON><PERSON><PERSON> (Simplified) (Runtime: 0.0636 s)\n", "  Worst Performer: SPADE (Simplified) (Runtime: 70.9546 s)\n"]}], "source": ["import pandas as pd\n", "if results: \n", "    df_perf = pd.DataFrame(results)\n", "\n", "    print(\"\\n--- Algorithm Performance Metrics ---\")\n", "    display(df_perf)\n", "\n", "    # Best and Worst Performer per min_support\n", "    print(\"\\n--- Best and Worst Performers (Based on Runtime) ---\")\n", "    for support in sorted(df_perf['Min Support'].unique()):\n", "        subset = df_perf[df_perf['Min Support'] == support]\n", "        best = subset.loc[subset['Runtime (s)'].idxmin()]\n", "        worst = subset.loc[subset['Runtime (s)'].idxmax()]\n", "\n", "        print(f\"\\nMin Support = {support}:\")\n", "        print(f\"  Best Performer: {best['Algorithm']} (Runtime: {best['Runtime (s)']:.4f} s)\")\n", "        print(f\"  Worst Performer: {worst['Algorithm']} (Runtime: {worst['Runtime (s)']:.4f} s)\")\n", "else:\n", "    print(\" The 'results' list is empty. Cannot generate performance metrics.\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}